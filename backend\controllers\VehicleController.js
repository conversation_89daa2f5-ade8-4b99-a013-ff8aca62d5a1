const mongoose = require('mongoose');
const Vehicle = require('../models/Vehicle');
const User = require('../models/User');
const path = require('path');
const fs = require('fs');
const fsPromises = fs.promises;
const imageType = require('image-type');
const imageSize = require('image-size');

const uploadDir = path.join(__dirname, '../uploads'); // Correction: utiliser 'uploads' en minuscule

exports.getVehiclesByDriverId = async (req, res) => {
  try {
    const driverId = req.params.driverId;
    console.log('Recherche des véhicules pour le conducteur ID:', driverId);

    // Vérifier si l'ID est valide
    if (!mongoose.Types.ObjectId.isValid(driverId)) {
      console.error('ID du conducteur invalide:', driverId);
      return res.status(400).json({ message: 'ID du conducteur invalide' });
    }

    const vehicles = await Vehicle.find({ driverId: driverId });
    console.log('Véhicules trouvés:', vehicles.length);

    // Log détaillé des véhicules trouvés
    vehicles.forEach((vehicle, index) => {
      console.log(`Véhicule ${index + 1}:`, {
        id: vehicle._id.toString(),
        driverId: vehicle.driverId.toString(),
        brand: vehicle.brand,
        model: vehicle.model,
        plateNumber: vehicle.plateNumber
      });
    });

    res.json(vehicles);
  } catch (error) {
    console.error("Erreur lors de la récupération des véhicules :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

exports.checkPlateUnique = async (req, res) => {
  try {
    const vehicle = await Vehicle.findOne({ plateNumber: req.params.plateNumber });
    if (vehicle) {
      return res.status(400).json({ message: 'Immatriculation déjà utilisée' });
    }
    res.status(200).json({ available: true });
  } catch (error) {
    console.error("Erreur lors de la vérification de l'immatriculation :", error);
    res.status(500).json({ message: 'Erreur serveur lors de la vérification de l\'immatriculation', error: error.message });
  }
};

exports.createVehicle = async (req, res) => {
  try {
    const {
      driverId,
      brand,
      model,
      year,
      plateNumber,
      seats,
      allowBaggage,
      exteriorImageUrl,
      interiorImageUrl,
    } = req.body;

    console.log('Données reçues:', req.body);

    if (!mongoose.Types.ObjectId.isValid(driverId)) {
      console.error('ID du conducteur invalide:', driverId);
      return res.status(400).json({ message: 'ID du conducteur invalide' });
    }

    const user = await User.findById(driverId);
    if (!user) {
      console.error('Utilisateur non trouvé:', driverId);
      return res.status(400).json({ message: 'Conducteur non trouvé' });
    }
    if (user.role !== 'conducteur') {
      console.error('Utilisateur non autorisé, rôle:', user.role);
      return res.status(403).json({ message: 'L\'utilisateur n\'est pas un conducteur' });
    }

    const existingVehicle = await Vehicle.findOne({ plateNumber });
    if (existingVehicle) {
      console.error('Immatriculation déjà utilisée:', plateNumber);
      return res.status(400).json({ message: 'Numéro d\'immatriculation déjà utilisé' });
    }

    if (!brand || !model || !year || !plateNumber || !seats || allowBaggage === undefined) {
      console.error('Champs manquants:', { brand, model, year, plateNumber, seats, allowBaggage });
      return res.status(400).json({ message: 'Tous les champs obligatoires doivent être fournis' });
    }

    const vehicle = new Vehicle({
      driverId,
      brand,
      model,
      year: parseInt(year),
      plateNumber,
      seats: parseInt(seats),
      allowBaggage: Boolean(allowBaggage),
      exteriorImageUrl,
      interiorImageUrl,
    });

    console.log('Création du véhicule avec driverId:', driverId);
    console.log('Type de driverId:', typeof driverId);
    console.log('URLs sauvegardées:', { exteriorImageUrl, interiorImageUrl });

    await vehicle.save();

    console.log('Véhicule créé avec succès:');
    console.log('  ID véhicule:', vehicle._id.toString());
    console.log('  DriverId enregistré:', vehicle.driverId.toString());
    console.log('  DriverId original:', driverId);

    res.status(201).json({ message: 'Véhicule créé avec succès', vehicle });
  } catch (error) {
    console.error('Erreur lors de la création du véhicule:', error);
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ message: 'Erreur de validation', errors });
    }
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

exports.updateVehicle = async (req, res) => {
  try {
    console.log('Mise à jour du véhicule - vehicleId:', req.params.vehicleId);
    const vehicle = await Vehicle.findById(req.params.vehicleId);
    if (!vehicle) {
      console.error('Véhicule non trouvé:', req.params.vehicleId);
      return res.status(404).json({ message: 'Voiture non trouvée' });
    }
    console.log('Vérification autorisation - driverId:', vehicle.driverId.toString(), 'req.user.id:', req.user.id);
    if (vehicle.driverId.toString() !== req.user.id) {
      console.error('Échec autorisation - driverId:', vehicle.driverId.toString(), '!= req.user.id:', req.user.id);
      return res.status(403).json({ message: 'Non autorisé' });
    }

    const { brand, model, year, plateNumber, seats, allowBaggage, exteriorImageUrl, interiorImageUrl } = req.body;

    console.log('Données reçues pour mise à jour:', req.body);

    if (!brand || !model || !year || !plateNumber || !seats || allowBaggage === undefined) {
      console.error('Champs manquants:', { brand, model, year, plateNumber, seats, allowBaggage });
      return res.status(400).json({ message: 'Tous les champs obligatoires doivent être fournis' });
    }

    if (plateNumber !== vehicle.plateNumber) {
      const existingVehicle = await Vehicle.findOne({ plateNumber });
      if (existingVehicle && existingVehicle._id.toString() !== req.params.vehicleId) {
        console.error('Immatriculation déjà utilisée:', plateNumber);
        return res.status(400).json({ message: 'Numéro d\'immatriculation déjà utilisé' });
      }
    }

    const updatedVehicle = await Vehicle.findByIdAndUpdate(
      req.params.vehicleId,
      {
        brand,
        model,
        year: parseInt(year),
        plateNumber,
        seats: parseInt(seats),
        allowBaggage,
        exteriorImageUrl,
        interiorImageUrl,
      },
      { new: true, runValidators: true }
    );
    console.log('URLs sauvegardées:', { exteriorImageUrl, interiorImageUrl });
    console.log('Véhicule mis à jour avec succès:', updatedVehicle);
    res.json(updatedVehicle);
  } catch (error) {
    console.error("Erreur lors de la mise à jour du véhicule :", error);
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ message: 'Erreur de validation', errors });
    }
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

exports.deleteVehicle = async (req, res) => {
  try {
    const vehicle = await Vehicle.findById(req.params.vehicleId);
    if (!vehicle) return res.status(404).json({ message: 'Voiture non trouvée' });
    if (vehicle.driverId.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Non autorisé' });
    }
    await Vehicle.findByIdAndDelete(req.params.vehicleId);
    res.json({ message: 'Voiture supprimée' });
  } catch (error) {
    console.error("Erreur lors de la suppression du véhicule :", error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

exports.uploadImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'Aucun fichier uploadé' });
    }

    console.log(`Tentative d'upload: ${req.file.originalname}, Taille: ${req.file.size} bytes, MIME: ${req.file.mimetype}`);

    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

    // Validation du type MIME avec gestion d'erreur améliorée
    let imgType;
    let finalMimeType;

    try {
      imgType = await imageType(req.file.buffer);
      if (imgType && allowedTypes.includes(imgType.mime)) {
        finalMimeType = imgType.mime;
        console.log(`Type MIME détecté par imageType: ${finalMimeType}`);
      } else {
        throw new Error('Type non détecté par imageType');
      }
    } catch (typeError) {
      console.warn('Erreur lors de la détection du type MIME, utilisation du type multer:', typeError.message);

      // Fallback sur le type MIME fourni par multer
      if (allowedTypes.includes(req.file.mimetype)) {
        finalMimeType = req.file.mimetype;
        console.log(`Type MIME utilisé depuis multer: ${finalMimeType}`);
      } else {
        // Dernier fallback basé sur l'extension
        const ext = req.file.originalname.toLowerCase().split('.').pop();
        switch (ext) {
          case 'jpg':
          case 'jpeg':
            finalMimeType = 'image/jpeg';
            break;
          case 'png':
            finalMimeType = 'image/png';
            break;
          case 'gif':
            finalMimeType = 'image/gif';
            break;
          case 'webp':
            finalMimeType = 'image/webp';
            break;
          default:
            console.error(`Type de fichier non supporté: ${req.file.originalname}, Extension: ${ext}, MIME multer: ${req.file.mimetype}`);
            return res.status(400).json({ message: 'Type de fichier non supporté. Utilisez JPEG, PNG, GIF ou WebP.' });
        }
        console.log(`Type MIME utilisé depuis l'extension: ${finalMimeType}`);
      }
    }

    console.log(`Type MIME final validé: ${finalMimeType}`);

    // Vérifier les dimensions de l'image (optionnel, plus tolérant)
    try {
      const dimensions = imageSize(req.file.buffer);
      if (dimensions && dimensions.width && dimensions.height) {
        console.log(`Image validée: ${req.file.originalname}, Taille: ${dimensions.width}x${dimensions.height}, Type: ${finalMimeType}`);
      } else {
        console.warn(`Impossible de détecter les dimensions pour ${req.file.originalname}, mais on continue...`);
      }
    } catch (error) {
      console.warn('Erreur lors de la validation des dimensions de l\'image (non bloquante):', error);
      // Ne pas bloquer l'upload si on ne peut pas détecter les dimensions
      console.log(`Poursuite de l'upload pour ${req.file.originalname} malgré l'erreur de dimensions`);
    }

    // Créer le nom de fichier sécurisé
    const sanitizedName = req.file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filename = `${Date.now()}-${sanitizedName}`;
    const filePath = path.join(uploadDir, filename);

    // S'assurer que le répertoire existe
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Écrire le fichier
    await fsPromises.writeFile(filePath, req.file.buffer);

    // Vérifier que le fichier a été créé
    if (!fs.existsSync(filePath)) {
      throw new Error('Échec de l\'enregistrement du fichier');
    }

    const imageUrl = `${process.env.BASE_URL}/uploads/${filename}`;
    console.log(`Image uploadée avec succès: ${imageUrl}, Taille du fichier: ${req.file.size} bytes`);

    res.status(200).json({
      imageUrl,
      filename,
      size: req.file.size,
      mimeType: finalMimeType
    });
  } catch (error) {
    console.error('Erreur lors de l\'upload:', error);
    res.status(500).json({ message: 'Erreur serveur lors de l\'upload', error: error.message });
  }
};