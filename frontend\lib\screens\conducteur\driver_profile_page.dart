import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:image_picker/image_picker.dart';
import 'package:universal_html/html.dart' as html;
import '/utils/keyboard_utils.dart';
import '/config/api_config.dart';
import '/services/api_service.dart';
import '/services/auth_service.dart';
import 'dart:io' show File;
import 'package:mime/mime.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '/widgets/network_image_widget.dart';

class DriverProfilePage extends StatefulWidget {
  final String userId;

  const DriverProfilePage({Key? key, required this.userId}) : super(key: key);

  @override
  _DriverProfilePageState createState() => _DriverProfilePageState();
}

class _DriverProfilePageState extends State<DriverProfilePage> with KeyboardUtils {
  Map<String, dynamic>? userData;
  List<dynamic>? vehicles;
  bool isLoading = true;
  bool isEditing = false;
  bool _isDialogOpen = false;
  final _formKey = GlobalKey<FormState>();
  final _vehicleFormKey = GlobalKey<FormState>();
  final TextEditingController nomController = TextEditingController();
  final TextEditingController prenomController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController villeController = TextEditingController();
  final TextEditingController numeroPermisController = TextEditingController();
  final TextEditingController yearController = TextEditingController();
  final TextEditingController plateNumberController = TextEditingController();
  final TextEditingController seatsController = TextEditingController();
  String? selectedBrand;
  String? selectedModel;
  String? selectedBaggage = 'Oui';
  XFile? exteriorImage;
  XFile? interiorImage;
  String? exteriorImageUrl;
  String? interiorImageUrl;
  XFile? profileImage; // Ajout pour l'image de profil
  String? profileImageUrl; // Ajout pour l'URL de l'image de profil

  final Map<String, List<String>> brandModels = {
    'Toyota': ['Corolla', 'Camry', 'Rav4', 'Hilux'],
    'Volkswagen': ['Golf', 'Passat', 'Tiguan', 'Polo'],
    'Ford': ['Fiesta', 'Focus', 'Mustang', 'F-150'],
    'Mercedes': ['C-Class', 'E-Class', 'S-Class', 'GLA'],
    'BMW': ['3 Series', '5 Series', 'X5', 'M3'],
  };

  @override
  void initState() {
    super.initState();
    phoneController.addListener(_autoAddCountryCode);
    fetchProfileData();
  }

  void _autoAddCountryCode() {
    final currentText = phoneController.text;
    if (currentText.isNotEmpty && !currentText.startsWith('+216')) {
      if (!currentText.startsWith('+')) {
        phoneController.text = '+216$currentText';
        phoneController.selection = TextSelection.fromPosition(
          TextPosition(offset: phoneController.text.length),
        );
      }
    }
  }

Future<String?> uploadImage(XFile image, {bool isProfileImage = false}) async {
  try {
    final allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    final mimeType = lookupMimeType(image.name, headerBytes: await image.readAsBytes()) ?? 'image/jpeg';
    print('Type MIME de l\'image: $mimeType');
    if (!allowedTypes.contains(mimeType)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Type de fichier non supporté. Utilisez JPEG, PNG, GIF ou WebP.')),
        );
      }
      return null;
    }

    // Convertir en JPEG
    final convertedImage = await convertToJpeg(image);
    if (convertedImage == null) {
      print('Échec de la conversion de l\'image');
      return null;
    }

    final token = await AuthService.getToken();
    if (token == null) {
      throw Exception('Token d\'authentification manquant');
    }

    var request = http.MultipartRequest(
      'POST',
      Uri.parse(isProfileImage
          ? '${ApiConfig.baseUrl}/api/users/${widget.userId}/upload-photo'
          : '${ApiConfig.baseUrl}/api/vehicles/upload-image'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.files.add(http.MultipartFile.fromBytes(
      isProfileImage ? 'photo' : 'image',
      await convertedImage.readAsBytes(),
      filename: convertedImage.name,
      contentType: MediaType('image', 'jpeg'),
    ));

    final response = await request.send().timeout(const Duration(seconds: 30));
    final responseBody = await response.stream.bytesToString();
    print('Réponse serveur: ${response.statusCode} - $responseBody');
    
    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      final imageUrl = data['imageUrl'] as String?;
      if (imageUrl == null || imageUrl.isEmpty) {
        throw Exception('URL d\'image non valide renvoyée par le serveur');
      }
      return imageUrl;
    } else {
      print('Erreur d\'upload: ${response.statusCode} - $responseBody');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(jsonDecode(responseBody)['message'] ?? 'Erreur d\'upload')),
        );
      }
      return null;
    }
  } catch (e) {
    print('Exception lors de l\'upload: $e');
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur lors de l\'upload: $e')),
      );
    }
    return null;
  }
}
  Future<void> updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      isLoading = true;
    });
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Erreur : Token d\'authentification manquant')),
          );
        }
        return;
      }

      String? newProfileImageUrl = profileImageUrl;
      if (profileImage != null) {
        newProfileImageUrl = await uploadImage(profileImage!, isProfileImage: true);
        if (newProfileImageUrl == null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Erreur d\'upload de l\'image de profil')),
            );
          }
          return;
        }
      }

      final data = {
        'nom': nomController.text.trim(),
        'prenom': prenomController.text.trim(),
        'email': emailController.text.trim(),
        'phone': phoneController.text.trim(),
        'ville': villeController.text.trim(),
        'numero_permis': numeroPermisController.text.trim(),
        if (newProfileImageUrl != null) 'photoUrl': newProfileImageUrl,
      };
      final response = await ApiService.put(
        '${ApiConfig.baseUrl}/api/users/${widget.userId}',
        data,
        token: token,
      );

      if (response.isSuccess) {
        setState(() {
          isEditing = false;
          userData = jsonDecode(response.body) as Map<String, dynamic>;
          nomController.text = userData?['nom'] as String? ?? '';
          prenomController.text = userData?['prenom'] as String? ?? '';
          emailController.text = userData?['email'] as String? ?? '';
          phoneController.text = userData?['phone'] as String? ?? '';
          villeController.text = userData?['ville'] as String? ?? '';
          numeroPermisController.text = userData?['numero_permis'] as String? ?? '';
          profileImageUrl = userData?['photoUrl'] as String?;
          profileImage = null;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Profil mis à jour avec succès')),
          );
        }
      } else {
        String errorMessage;
        try {
          final data = jsonDecode(response.body) as Map<String, dynamic>;
          errorMessage = data['message'] as String? ?? ApiConfig.getErrorMessage(response.statusCode);
        } catch (e) {
          errorMessage = ApiConfig.getErrorMessage(response.statusCode);
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(errorMessage)),
          );
        }
        return;
      }
    } catch (e) {
      print("Erreur lors de la mise à jour du profil : $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(ApiConfig.getConnectionErrorMessage(e))),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> fetchProfileData() async {
    setState(() {
      isLoading = true;
    });
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Erreur : Token d\'authentification manquant')),
          );
        }
        return;
      }
      final userResponse = await ApiService.get(
        '${ApiConfig.baseUrl}/api/users/${widget.userId}',
        token: token,
      );

      if (userResponse.body.startsWith('<!DOCTYPE')) {
        print('Réponse HTML détectée pour /api/users: ${userResponse.body}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur : Serveur inaccessible (ngrok bloque la requête). Visitez l\'URL dans un navigateur pour autoriser l\'accès.'),
            ),
          );
        }
        return;
      }

      if (userResponse.isSuccess) {
        final data = jsonDecode(userResponse.body) as Map<String, dynamic>;
        if (data['role'] != 'conducteur') {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Utilisateur non autorisé à accéder à ce profil')),
            );
            Navigator.pop(context);
          }
          return;
        }
        setState(() {
          userData = data;
          nomController.text = userData?['nom'] as String? ?? '';
          prenomController.text = userData?['prenom'] as String? ?? '';
          emailController.text = userData?['email'] as String? ?? '';
          final phone = userData?['phone'] as String? ?? '';
          phoneController.text = phone.startsWith('+216') ? phone : '+216$phone';
          villeController.text = userData?['ville'] as String? ?? '';
          numeroPermisController.text = userData?['numero_permis'] as String? ?? '';
          profileImageUrl = userData?['photoUrl'] as String?;
        });
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(ApiConfig.getErrorMessage(userResponse.statusCode))),
          );
        }
      }

      print('Tentative de récupération des véhicules pour userId: ${widget.userId}');
      final vehiclesResponse = await ApiService.get(
        '${ApiConfig.baseUrl}/api/vehicles/driver/${widget.userId}',
        token: token,
      );
      print('Récupération des véhicules - Statut: ${vehiclesResponse.statusCode}, Corps: ${vehiclesResponse.body}');
      if (vehiclesResponse.body.startsWith('<!DOCTYPE')) {
        print('Réponse HTML détectée pour /api/vehicles/driver: ${vehiclesResponse.body}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur : Serveur inaccessible (ngrok bloque la requête). Visitez l\'URL dans un navigateur pour autoriser l\'accès.'),
            ),
          );
        }
        return;
      }
      if (vehiclesResponse.isSuccess) {
        setState(() {
          vehicles = jsonDecode(vehiclesResponse.body) as List<dynamic>?;
          vehicles?.forEach((vehicle) {
            print('Véhicule ID: ${vehicle['_id']}');
            print('Image extérieure: ${vehicle['exteriorImageUrl']}');
            print('Image intérieure: ${vehicle['interiorImageUrl']}');
          });
        });
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(ApiConfig.getErrorMessage(vehiclesResponse.statusCode))),
          );
        }
      }
    } catch (e) {
      print("Erreur lors de la récupération des données : $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(ApiConfig.getConnectionErrorMessage(e))),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> addVehicle() async {
    if (!_vehicleFormKey.currentState!.validate()) {
      print('Validation du formulaire échouée');
      return;
    }

    final plateNumber = plateNumberController.text.trim();
    if (plateNumber.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Numéro d\'immatriculation requis')),
        );
      }
      return;
    }

    print('Début de l\'ajout du véhicule');
    print('Numéro d\'immatriculation: $plateNumber');

    final token = await AuthService.getToken();
    if (token == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Erreur : Token d\'authentification manquant')),
        );
      }
      print('Token manquant');
      return;
    }

    try {
      final checkResponse = await ApiService.get(
        '${ApiConfig.baseUrl}/api/vehicles/plate/$plateNumber',
        token: token,
      );
      print('Vérification unicité - Statut: ${checkResponse.statusCode}, Corps: ${checkResponse.body}');

      if (checkResponse.statusCode == 400) {
        if (mounted) {
          final errorData = jsonDecode(checkResponse.body) as Map<String, dynamic>?;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(errorData?['message'] as String? ?? 'Immatriculation déjà utilisée')),
          );
        }
        return;
      } else if (!checkResponse.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(ApiConfig.getErrorMessage(checkResponse.statusCode))),
          );
        }
        return;
      }
    } catch (e) {
      print('Erreur lors de la vérification de l\'immatriculation: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(ApiConfig.getConnectionErrorMessage(e))),
        );
      }
      return;
    }

    String? exteriorImageUrlTemp;
    String? interiorImageUrlTemp;

    if (exteriorImage != null) {
      exteriorImageUrlTemp = await uploadImage(exteriorImage!);
      if (exteriorImageUrlTemp == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Erreur d\'upload de l\'image extérieure')),
          );
        }
        return;
      }
    }
    if (interiorImage != null) {
      interiorImageUrlTemp = await uploadImage(interiorImage!);
      if (interiorImageUrlTemp == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Erreur d\'upload de l\'image intérieure')),
          );
        }
        return;
      }
    }

    setState(() {
      isLoading = true;
      exteriorImageUrl = exteriorImageUrlTemp;
      interiorImageUrl = interiorImageUrlTemp;
    });

    try {
      final driverId = widget.userId;
      print('Données envoyées au serveur:');
      print('Driver ID: $driverId');
      print('Marque: $selectedBrand');
      print('Modèle: $selectedModel');
      print('Année: ${yearController.text}');
      print('Immatriculation: $plateNumber');
      print('Sièges: ${seatsController.text}');
      print('Bagages: $selectedBaggage');
      print('Image extérieure: $exteriorImageUrl');
      print('Image intérieure: $interiorImageUrl');

      final data = {
        'driverId': driverId,
        'brand': selectedBrand,
        'model': selectedModel,
        'year': int.parse(yearController.text.trim()),
        'plateNumber': plateNumber,
        'seats': int.parse(seatsController.text.trim()),
        'allowBaggage': selectedBaggage == 'Oui',
        if (exteriorImageUrlTemp != null) 'exteriorImageUrl': exteriorImageUrlTemp,
        if (interiorImageUrlTemp != null) 'interiorImageUrl': interiorImageUrlTemp,
      };

      final response = await ApiService.post(
        '${ApiConfig.baseUrl}/api/vehicles',
        data,
        token: token,
      );

      print('Réponse serveur - Statut: ${response.statusCode}, Corps: ${response.body}');

      if (response.isSuccess) {
        print('Véhicule ajouté avec succès');
        await fetchProfileData();
        setState(() {
          selectedBrand = null;
          selectedModel = null;
          selectedBaggage = 'Oui';
          exteriorImage = null;
          interiorImage = null;
          exteriorImageUrl = null;
          interiorImageUrl = null;
          yearController.clear();
          plateNumberController.clear();
          seatsController.clear();
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Voiture ajoutée avec succès')),
          );
        }
      } else {
        String errorMessage;
        try {
          final errorData = jsonDecode(response.body) as Map<String, dynamic>;
          errorMessage = errorData['message'] as String? ?? ApiConfig.getErrorMessage(response.statusCode);
        } catch (e) {
          errorMessage = ApiConfig.getErrorMessage(response.statusCode);
        }
        print('Erreur serveur: $errorMessage');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(errorMessage)),
          );
        }
      }
    } catch (e) {
      print('Exception lors de l\'ajout du véhicule: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e')),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

Future<XFile?> convertToJpeg(XFile image) async {
  try {
    final bytes = await image.readAsBytes();
    final imageData = img.decodeImage(bytes);
    if (imageData == null) {
      print('Impossible de décoder l\'image: ${image.name}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Impossible de décoder l\'image. Vérifiez le format.')),
        );
      }
      return null;
    }

    // Vérifier les dimensions
    if (imageData.width < 1 || imageData.height < 1) {
      print('Dimensions d\'image invalides: ${imageData.width}x${imageData.height}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Dimensions d\'image invalides')),
        );
      }
      return null;
    }

    // Convertir en JPEG
    final jpegBytes = img.encodeJpg(imageData, quality: 85);
    if (jpegBytes.isEmpty) {
      print('Échec de l\'encodage en JPEG');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Échec de la conversion en JPEG')),
        );
      }
      return null;
    }

    if (kIsWeb) {
      final blob = html.Blob([jpegBytes], 'image/jpeg');
      final url = html.Url.createObjectUrlFromBlob(blob);
      return XFile(url, mimeType: 'image/jpeg', name: '${image.name.split('.').first}.jpg');
    } else {
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/${image.name.split('.').first}.jpg');
      await tempFile.writeAsBytes(jpegBytes);
      return XFile(tempFile.path, mimeType: 'image/jpeg', name: tempFile.path.split('/').last);
    }
  } catch (e) {
    print('Erreur lors de la conversion de l\'image: $e');
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur lors de la conversion de l\'image: $e')),
      );
    }
    return null;
  }
}
  Future<void> updateVehicle(String vehicleId) async {
    if (!_vehicleFormKey.currentState!.validate()) {
      print('Validation du formulaire échouée');
      return;
    }

    final plateNumber = plateNumberController.text.trim();
    if (plateNumber.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Numéro d\'immatriculation requis')),
        );
      }
      print('Numéro d\'immatriculation manquant');
      return;
    }

    print('Début de la mise à jour du véhicule: $vehicleId');
    print('Numéro d\'immatriculation: $plateNumber');

    final token = await AuthService.getToken();
    if (token == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Erreur : Token d\'authentification manquant')),
        );
      }
      print('Token manquant');
      return;
    }

    final vehicle = vehicles?.firstWhere((v) => v['_id'] == vehicleId, orElse: () => null);
    if (vehicle != null && vehicle['plateNumber'] != plateNumber) {
      try {
        final checkResponse = await ApiService.get(
          '${ApiConfig.baseUrl}/api/vehicles/plate/$plateNumber',
          token: token,
        );
        print('Vérification unicité - Statut: ${checkResponse.statusCode}, Corps: ${checkResponse.body}');

        if (checkResponse.statusCode == 400) {
          if (mounted) {
            final errorData = jsonDecode(checkResponse.body) as Map<String, dynamic>?;
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(errorData?['message'] as String? ?? 'Immatriculation déjà utilisée')),
            );
          }
          return;
        } else if (!checkResponse.isSuccess) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(ApiConfig.getErrorMessage(checkResponse.statusCode))),
            );
          }
          return;
        }
      } catch (e) {
        print('Erreur lors de la vérification de l\'immatriculation: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(ApiConfig.getConnectionErrorMessage(e))),
          );
        }
        return;
      }
    }

    String? exteriorImageUrlTemp = exteriorImageUrl;
    String? interiorImageUrlTemp = interiorImageUrl;

    if (exteriorImage != null) {
      exteriorImageUrlTemp = await uploadImage(exteriorImage!);
      if (exteriorImageUrlTemp == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Erreur d\'upload de l\'image extérieure')),
          );
        }
        return;
      }
    }
    if (interiorImage != null) {
      interiorImageUrlTemp = await uploadImage(interiorImage!);
      if (interiorImageUrlTemp == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Erreur d\'upload de l\'image intérieure')),
          );
        }
        return;
      }
    }

    setState(() {
      isLoading = true;
    });

    try {
      final data = {
        'brand': selectedBrand,
        'model': selectedModel,
        'year': int.parse(yearController.text.trim()),
        'plateNumber': plateNumber,
        'seats': int.parse(seatsController.text.trim()),
        'allowBaggage': selectedBaggage == 'Oui',
        if (exteriorImageUrlTemp != null) 'exteriorImageUrl': exteriorImageUrlTemp,
        if (interiorImageUrlTemp != null) 'interiorImageUrl': interiorImageUrlTemp,
      };

      print('Données envoyées pour mise à jour:');
      print(jsonEncode(data));

      final response = await ApiService.put(
        '${ApiConfig.baseUrl}/api/vehicles/$vehicleId',
        data,
        token: token,
      );

      print('Réponse serveur - Statut: ${response.statusCode}, Corps: ${response.body}');

      if (response.isSuccess) {
        print('Véhicule mis à jour avec succès');
        await fetchProfileData();
        setState(() {
          selectedBrand = null;
          selectedModel = null;
          selectedBaggage = 'Oui';
          exteriorImage = null;
          interiorImage = null;
          exteriorImageUrl = null;
          interiorImageUrl = null;
          yearController.clear();
          plateNumberController.clear();
          seatsController.clear();
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Voiture mise à jour avec succès')),
          );
        }
      } else {
        String errorMessage;
        try {
          final errorData = jsonDecode(response.body) as Map<String, dynamic>;
          errorMessage = errorData['message'] as String? ?? ApiConfig.getErrorMessage(response.statusCode);
        } catch (e) {
          errorMessage = ApiConfig.getErrorMessage(response.statusCode);
        }
        print('Erreur serveur: $errorMessage');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(errorMessage)),
          );
        }
      }
    } catch (e) {
      print('Erreur lors de la mise à jour du véhicule: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e')),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  void showVehicleDialog({Map<String, dynamic>? vehicle}) {
    if (_isDialogOpen) return;
    setState(() {
      _isDialogOpen = true;
    });

    if (vehicle != null) {
      setState(() {
        selectedBrand = vehicle['brand'] as String?;
        selectedModel = vehicle['model'] as String?;
        yearController.text = (vehicle['year'] as int?)?.toString() ?? '';
        plateNumberController.text = vehicle['plateNumber'] as String? ?? '';
        seatsController.text = (vehicle['seats'] as int?)?.toString() ?? '';
        selectedBaggage = (vehicle['allowBaggage'] as bool?) == true ? 'Oui' : 'Non';
        exteriorImage = null;
        interiorImage = null;
        exteriorImageUrl = vehicle['exteriorImageUrl'] as String?;
        interiorImageUrl = vehicle['interiorImageUrl'] as String?;
      });
    } else {
      setState(() {
        selectedBrand = null;
        selectedModel = null;
        selectedBaggage = 'Oui';
        exteriorImage = null;
        interiorImage = null;
        exteriorImageUrl = null;
        interiorImageUrl = null;
        yearController.clear();
        plateNumberController.clear();
        seatsController.clear();
      });
    }

    showDialog(
      context: context,
      builder: (context) => KeyboardAwareDialog(
        title: vehicle != null ? 'Modifier la voiture' : 'Ajouter une voiture',
        content: StatefulBuilder(
          builder: (context, setDialogState) => Form(
            key: _vehicleFormKey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  DropdownButtonFormField<String>(
                    value: selectedBrand,
                    decoration: const InputDecoration(labelText: 'Marque'),
                    items: brandModels.keys.map((brand) {
                      return DropdownMenuItem<String>(
                        value: brand,
                        child: Text(brand),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedBrand = value;
                        selectedModel = null;
                      });
                      setState(() {
                        selectedBrand = value;
                        selectedModel = null;
                      });
                    },
                    validator: (value) => value == null ? 'Marque obligatoire' : null,
                  ),
                  if (selectedBrand != null)
                    DropdownButtonFormField<String>(
                      value: selectedModel,
                      decoration: const InputDecoration(labelText: 'Modèle'),
                      items: brandModels[selectedBrand]!.map((model) {
                        return DropdownMenuItem<String>(
                          value: model,
                          child: Text(model),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setDialogState(() {
                          selectedModel = value;
                        });
                        setState(() {
                          selectedModel = value;
                        });
                      },
                      validator: (value) => value == null ? 'Modèle obligatoire' : null,
                    ),
                  TextFormField(
                    controller: yearController,
                    decoration: const InputDecoration(labelText: 'Année'),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Année obligatoire';
                      if (int.tryParse(value) == null) return 'Année doit être un nombre';
                      return null;
                    },
                  ),
                  TextFormField(
                    controller: plateNumberController,
                    decoration: const InputDecoration(labelText: 'Numéro d\'immatriculation'),
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Numéro obligatoire';
                      if (!RegExp(r'^\d{2,3}TU\d{2,4}$').hasMatch(value)) {
                        return 'Format invalide (ex: 123TU456 ou 12TU34)';
                      }
                      return null;
                    },
                  ),
                  TextFormField(
                    controller: seatsController,
                    decoration: const InputDecoration(labelText: 'Nombre de sièges'),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Nombre de sièges obligatoire';
                      final seats = int.tryParse(value);
                      if (seats == null || seats < 1 || seats > 8) return 'Entre 1 et 8 sièges';
                      return null;
                    },
                  ),
                  DropdownButtonFormField<String>(
                    value: selectedBaggage,
                    decoration: const InputDecoration(labelText: 'Bagages autorisés'),
                    items: ['Oui', 'Non'].map((option) {
                      return DropdownMenuItem<String>(
                        value: option,
                        child: Text(option),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setDialogState(() {
                        selectedBaggage = value;
                      });
                      setState(() {
                        selectedBaggage = value;
                      });
                    },
                    validator: (value) => value == null ? 'Bagages obligatoire' : null,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () async {
                      final picker = ImagePicker();
                      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
                      if (pickedFile != null) {
                        final convertedFile = await convertToJpeg(pickedFile);
                        if (convertedFile != null) {
                          print('Image extérieure convertie: ${convertedFile.path}, MIME: ${convertedFile.mimeType}');
                          setDialogState(() {
                            exteriorImage = convertedFile;
                            exteriorImageUrl = null;
                          });
                          setState(() {
                            exteriorImage = convertedFile;
                            exteriorImageUrl = null;
                          });
                        } else {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Erreur lors de la conversion de l\'image')),
                            );
                          }
                        }
                      }
                    },
                    child: const Text('Ajouter image extérieure'),
                  ),
                  if (exteriorImage != null)
                    Text('Image extérieure sélectionnée : ${exteriorImage!.name}'),
                  if (exteriorImageUrl != null && exteriorImageUrl!.isNotEmpty)
                    Column(
                      children: [
                        const Text('Image extérieure actuelle :'),
                    CachedNetworkImage(
  imageUrl: exteriorImageUrl!,
  height: 100,
  width: 100,
  fit: BoxFit.cover,
  placeholder: (context, url) => const CircularProgressIndicator(),
  errorWidget: (context, url, error) {
    print('Erreur de chargement de l\'image extérieure: $error, URL: $url');
    return const Text('Erreur de chargement de l\'image extérieure');
  },
),
                      ],
                    ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () async {
                      final picker = ImagePicker();
                      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
                      if (pickedFile != null) {
                        final convertedFile = await convertToJpeg(pickedFile);
                        if (convertedFile != null) {
                          print('Image intérieure convertie: ${convertedFile.path}, MIME: ${convertedFile.mimeType}');
                          setDialogState(() {
                            interiorImage = convertedFile;
                            interiorImageUrl = null;
                          });
                          setState(() {
                            interiorImage = convertedFile;
                            interiorImageUrl = null;
                          });
                        } else {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Erreur lors de la conversion de l\'image')),
                            );
                          }
                        }
                      }
                    },
                    child: const Text('Ajouter image intérieure'),
                  ),
                  if (interiorImage != null)
                    Text('Image intérieure sélectionnée : ${interiorImage!.name}'),
                  if (interiorImageUrl != null && interiorImageUrl!.isNotEmpty)
                    Column(
                      children: [
                        const Text('Image intérieure actuelle :'),
                       CachedNetworkImage(
  imageUrl: interiorImageUrl!,
  height: 100,
  width: 100,
  fit: BoxFit.cover,
  placeholder: (context, url) => const CircularProgressIndicator(),
  errorWidget: (context, url, error) {
    print('Erreur de chargement de l\'image intérieure: $error, URL: $url');
    return const Text('Erreur de chargement de l\'image intérieure');
  },
),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_vehicleFormKey.currentState!.validate()) {
                if (vehicle != null) {
                  updateVehicle(vehicle['_id'] as String);
                } else {
                  addVehicle();
                }
                Navigator.pop(context);
              }
            },
            child: Text(vehicle != null ? 'Modifier' : 'Ajouter'),
          ),
        ],
      ),
    ).then((_) {
      setState(() {
        _isDialogOpen = false;
      });
    });
  }

  Future<void> deleteVehicle(String vehicleId) async {
    setState(() {
      isLoading = true;
    });
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Erreur : Token d\'authentification manquant')),
          );
        }
        return;
      }
      final response = await http.delete(
        Uri.parse('${ApiConfig.baseUrl}/api/vehicles/$vehicleId'),
        headers: {
          ...ApiConfig.defaultHeaders,
          'Authorization': 'Bearer $token',
        },
      ).timeout(const Duration(seconds: 30));
      if (response.statusCode == 200) {
        await fetchProfileData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Voiture supprimée avec succès')),
          );
        }
      } else {
        if (mounted) {
          String errorMessage;
          try {
            final errorData = jsonDecode(response.body) as Map<String, dynamic>;
            errorMessage = errorData['message'] as String? ?? ApiConfig.getErrorMessage(response.statusCode);
          } catch (e) {
            errorMessage = ApiConfig.getErrorMessage(response.statusCode);
          }
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(errorMessage)),
          );
        }
      }
    } catch (e) {
      print("Erreur lors de la suppression de la voiture : $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(ApiConfig.getConnectionErrorMessage(e))),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    phoneController.removeListener(_autoAddCountryCode);
    nomController.dispose();
    prenomController.dispose();
    emailController.dispose();
    phoneController.dispose();
    villeController.dispose();
    numeroPermisController.dispose();
    yearController.dispose();
    plateNumberController.dispose();
    seatsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return buildKeyboardDismissWrapper(
      context: context,
      child: Scaffold(
        appBar: AppBar(title: const Text('Profil du Conducteur')),
        body: isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Column(
                        children: [
                          if (profileImageUrl != null && profileImageUrl!.isNotEmpty)
                            Column(
                              children: [
                                const Text('Photo de profil actuelle :'),
                            CachedNetworkImage(
  imageUrl: profileImageUrl!,
  imageBuilder: (context, imageProvider) => CircleAvatar(
    radius: 50,
    backgroundImage: imageProvider,
  ),
  placeholder: (context, url) => const CircularProgressIndicator(),
  errorWidget: (context, url, error) {
    print('Erreur de chargement de l\'image de profil: $error, URL: $url');
    return const CircleAvatar(
      radius: 50,
      child: Icon(Icons.person),
    );
  },
),
                              ],
                            )
                          else
                            const CircleAvatar(
                              radius: 50,
                              child: Icon(Icons.person),
                            ),
                          if (isEditing)
                            ElevatedButton(
                              onPressed: () async {
                                final picker = ImagePicker();
                                final pickedFile = await picker.pickImage(source: ImageSource.gallery);
                                if (pickedFile != null) {
                                  final convertedFile = await convertToJpeg(pickedFile);
                                  if (convertedFile != null) {
                                    setState(() {
                                      profileImage = convertedFile;
                                      profileImageUrl = null;
                                    });
                                  } else {
                                    if (mounted) {
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        const SnackBar(content: Text('Erreur lors de la conversion de l\'image')),
                                      );
                                    }
                                  }
                                }
                              },
                              child: const Text('Changer la photo de profil'),
                            ),
                          if (profileImage != null)
                            Text('Image sélectionnée : ${profileImage!.name}'),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text('Informations du Conducteur', style: Theme.of(context).textTheme.titleLarge),
                    const SizedBox(height: 16),
                    Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          TextFormField(
                            controller: nomController,
                            decoration: const InputDecoration(labelText: 'Nom'),
                            enabled: isEditing,
                            validator: (value) => value == null || value.isEmpty ? 'Nom obligatoire' : null,
                          ),
                          TextFormField(
                            controller: prenomController,
                            decoration: const InputDecoration(labelText: 'Prénom'),
                            enabled: isEditing,
                            validator: (value) => value == null || value.isEmpty ? 'Prénom obligatoire' : null,
                          ),
                          TextFormField(
                            controller: emailController,
                            decoration: const InputDecoration(labelText: 'Email'),
                            enabled: isEditing,
                            validator: (value) {
                              if (value == null || value.isEmpty) return 'Email obligatoire';
                              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                                return 'Email invalide';
                              }
                              return null;
                            },
                          ),
                          TextFormField(
                            controller: phoneController,
                            decoration: const InputDecoration(labelText: 'Téléphone'),
                            enabled: isEditing,
                            keyboardType: TextInputType.phone,
                            validator: (value) {
                              if (value == null || value.isEmpty) return 'Téléphone obligatoire';
                              if (value != (userData?['phone'] as String?) && !RegExp(r'^\+216[0-9]{8}$').hasMatch(value)) {
                                return 'Téléphone invalide (doit commencer par +216 suivi de 8 chiffres)';
                              }
                              return null;
                            },
                          ),
                          TextFormField(
                            controller: villeController,
                            decoration: const InputDecoration(labelText: 'Ville'),
                            enabled: isEditing,
                            validator: (value) => value == null || value.isEmpty ? 'Ville obligatoire' : null,
                          ),
                          TextFormField(
                            controller: numeroPermisController,
                            decoration: const InputDecoration(labelText: 'Numéro de permis'),
                            enabled: isEditing,
                            validator: (value) {
                              if (isEditing && (value == null || value.isEmpty)) return 'Numéro de permis obligatoire pour conducteurs';
                              if (isEditing && !RegExp(r'^[A-Za-z0-9]{6,}$').hasMatch(value!)) {
                                return 'Numéro de permis invalide';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              ElevatedButton(
                                onPressed: () {
                                  if (isEditing) {
                                    updateProfile();
                                  } else {
                                    setState(() {
                                      isEditing = true;
                                    });
                                  }
                                },
                                child: Text(isEditing ? 'Enregistrer' : 'Modifier'),
                              ),
                              if (isEditing)
                                TextButton(
                                  onPressed: () {
                                    setState(() {
                                      isEditing = false;
                                      nomController.text = userData?['nom'] as String? ?? '';
                                      prenomController.text = userData?['prenom'] as String? ?? '';
                                      emailController.text = userData?['email'] as String? ?? '';
                                      phoneController.text = (userData?['phone'] as String?)?.startsWith('+216') ?? false
                                          ? userData!['phone'] as String
                                          : '+216${userData?['phone'] as String? ?? ''}';
                                      villeController.text = userData?['ville'] as String? ?? '';
                                      numeroPermisController.text = userData?['numero_permis'] as String? ?? '';
                                      profileImage = null;
                                      profileImageUrl = userData?['photoUrl'] as String?;
                                    });
                                  },
                                  child: const Text('Annuler'),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),
                    Text('Voitures', style: Theme.of(context).textTheme.titleLarge),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isDialogOpen ? null : () => showVehicleDialog(),
                      child: const Text('Ajouter une voiture'),
                    ),
                    const SizedBox(height: 16),
                    vehicles == null || vehicles!.isEmpty
                        ? const Text('Aucune voiture enregistrée')
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: vehicles!.length,
                            itemBuilder: (context, index) {
                              final vehicle = vehicles![index] as Map<String, dynamic>;
                              return Card(
                                child: ListTile(
                                  title: Text('${vehicle['brand'] as String? ?? ''} ${vehicle['model'] as String? ?? ''}'),
                                  subtitle: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text('Année: ${vehicle['year']?.toString() ?? ''}'),
                                      Text('Immatriculation: ${vehicle['plateNumber'] as String? ?? ''}'),
                                      Text('Sièges: ${vehicle['seats']?.toString() ?? ''}'),
                                      Text('Bagages: ${(vehicle['allowBaggage'] as bool?) == true ? 'Oui' : 'Non'}'),
                                      Text('Statut: ${vehicle['status'] as String? ?? ''}'),
                                      if (vehicle['exteriorImageUrl'] != null && vehicle['exteriorImageUrl'].toString().isNotEmpty)
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            const Text('Image extérieure :'),
                                            NetworkImageWidget(
                                              imageUrl: vehicle['exteriorImageUrl'] as String,
                                              height: 100,
                                              width: 100,
                                              fit: BoxFit.cover,
                                              placeholder: 'Chargement image extérieure...',
                                              errorText: 'Image extérieure non disponible',
                                            ),
                                          ],
                                        ),
                                      if (vehicle['interiorImageUrl'] != null && vehicle['interiorImageUrl'].toString().isNotEmpty)
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            const Text('Image intérieure :'),
                                            NetworkImageWidget(
                                              imageUrl: vehicle['interiorImageUrl'] as String,
                                              height: 100,
                                              width: 100,
                                              fit: BoxFit.cover,
                                              placeholder: 'Chargement image intérieure...',
                                              errorText: 'Image intérieure non disponible',
                                            ),
                                          ],
                                        ),
                                    ],
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.edit),
                                        onPressed: () => showVehicleDialog(vehicle: vehicle),
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.delete),
                                        onPressed: () {
                                          showDialog(
                                            context: context,
                                            builder: (context) => AlertDialog(
                                              title: const Text('Confirmer la suppression'),
                                              content: const Text('Voulez-vous vraiment supprimer cette voiture ?'),
                                              actions: [
                                                TextButton(
                                                  onPressed: () => Navigator.pop(context),
                                                  child: const Text('Annuler'),
                                                ),
                                                ElevatedButton(
                                                  onPressed: () {
                                                    deleteVehicle(vehicle['_id'] as String);
                                                    Navigator.pop(context);
                                                  },
                                                  child: const Text('Supprimer'),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                  ],
                ),
              ),
      ),
    );
  }
}